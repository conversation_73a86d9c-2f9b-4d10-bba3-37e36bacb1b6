name: Build and Release PDF

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

permissions:
  contents: write
  packages: write
  pull-requests: read

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Typst
      uses: typst-community/setup-typst@v3
      with:
        typst-version: latest
        
    - name: Install fonts
      run: |
        # Create fonts directory for system fonts
        sudo mkdir -p /usr/share/fonts/truetype/custom
        
        # Copy project fonts to system fonts directory
        sudo cp fonts/*.ttf /usr/share/fonts/truetype/custom/ || true
        
        # Copy Times New Roman fonts if they exist
        if [ -d "fonts/TimesNewRoman" ]; then
          sudo cp fonts/TimesNewRoman/*.ttf /usr/share/fonts/truetype/custom/ || true
        fi
        
        # Update font cache
        sudo fc-cache -fv
        
        # List available fonts for debugging
        fc-list | grep -i "times\|simsun\|simhei\|kaiti\|fangsong" || echo "Custom fonts may not be loaded"
        
    - name: Build PDF
      run: |
        # Build the book PDF with archival quality settings
        # PDF/A-2b: Maximum compatibility, archival quality, extensive metadata
        typst compile --root . --pdf-standard a-2b template/book.typ book.pdf

        # Verify PDF was created
        if [ ! -f "book.pdf" ]; then
          echo "Error: PDF was not generated"
          exit 1
        fi

        # Show PDF info
        ls -la book.pdf
        echo "✅ PDF built with PDF/A-2b standard for maximum archival compatibility"
        
    - name: Upload PDF as artifact
      uses: actions/upload-artifact@v4
      with:
        name: sr-book-cn-pdf
        path: book.pdf
        retention-days: 30
        
    - name: Get commit info
      id: commit
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "commit_message=$(git log -1 --pretty=%B | head -1)" >> $GITHUB_OUTPUT
        echo "commit_date=$(git log -1 --pretty=%cd --date=short)" >> $GITHUB_OUTPUT
        echo "build_timestamp=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT
        echo "build_datetime=$(date '+%Y-%m-%d %H:%M:%S')" >> $GITHUB_OUTPUT
        
    - name: Create Release
      if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
      uses: softprops/action-gh-release@v1
      with:
        tag_name: build-${{ steps.commit.outputs.build_timestamp }}-${{ steps.commit.outputs.sha_short }}
        name: "Build ${{ steps.commit.outputs.sha_short }} - ${{ steps.commit.outputs.build_datetime }}"
        body: |
          ## Automated Build

          **Commit Message**: ${{ steps.commit.outputs.commit_message }}
          **Commit Hash**: ${{ steps.commit.outputs.sha_short }}
          **Build Time**: ${{ steps.commit.outputs.build_datetime }}
          **Commit Date**: ${{ steps.commit.outputs.commit_date }}

          This is an automatically built PDF version based on commit ${{ steps.commit.outputs.sha_short }}.

        files: |
          book.pdf
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Clean up old releases
      if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
      uses: dev-drprasad/delete-older-releases@v0.3.4
      with:
        repo: ${{ github.repository }}
        keep_latest: 64
        delete_tag_pattern: "^(latest-|build-)"
        delete_tags: true
        keep_min_download_counts: 0
        delete_expired_data: 0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Delete existing Latest releases
      if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
      run: |
        echo "🗑️ Cleaning up existing Latest releases..."
        gh release list --limit 20 --json tagName,name | jq -r '.[] | select(.name | contains("Latest")) | .tagName' | while read -r tag; do
          [ -n "$tag" ] && gh release delete "$tag" --yes --cleanup-tag 2>/dev/null || true
        done
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Create Latest release
      if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
      run: |
        RELEASE_TAG="latest-${{ steps.commit.outputs.build_timestamp }}"
        gh release create "$RELEASE_TAG" book.pdf \
          --title "Symphonic Rain Chinese Translation - Latest (${{ steps.commit.outputs.build_datetime }})" \
          --notes "🎯 **LATEST VERSION** - Download this for the most recent translation.

        **Commit**: ${{ steps.commit.outputs.commit_message }}
        **Hash**: ${{ steps.commit.outputs.sha_short }}
        **Build Time**: ${{ steps.commit.outputs.build_datetime }}
        **Commit Date**: ${{ steps.commit.outputs.commit_date }}

        📥 Download \`book.pdf\` for the latest Chinese translation." \
          --latest
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
