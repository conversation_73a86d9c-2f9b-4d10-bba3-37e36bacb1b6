# Symphonic Rain - Chinese Translation

An unofficial Chinese translation of Symphonic Rain.

## Download

Get the latest PDF from [Releases](https://github.com/kiritantakechi/sr-book-cn/releases/latest).

## Build

This project uses [Typst](https://typst.app/) for document generation.

```bash
# Standard build
typst compile --root . template/book.typ book.pdf

# Archival quality build (recommended)
typst compile --root . --pdf-standard a-2b template/book.typ book.pdf
```

## Roadmap

### Content Chapters
- [x] **Chapter 1**: Prelude
- [x] **Chapter 2**: I Part
- [ ] **Chapter 3**: II Part (WIP)
- [ ] **Chapter 4**: III Part

### Typography & Layout Improvements
- [ ] **Two-page Layout**: Professional book-style dual-page spread support

### Technical Enhancements
- [x] **PDF/A Compliance**: Archival-quality PDF generation

## License

The <a href="https://github.com/kiritantakechi/sr-book-cn">sr-book-cn</a> © 2025 by <a href="https://github.com/kiritantakechi">kiritan</a> is licensed under <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/">CC BY-NC-SA 4.0</a> <img src="https://mirrors.creativecommons.org/presskit/icons/cc.svg" alt="CC" style="height: 1em; width: 1em; vertical-align: text-bottom;"><img src="https://mirrors.creativecommons.org/presskit/icons/by.svg" alt="BY" style="height: 1em; width: 1em; vertical-align: text-bottom;"><img src="https://mirrors.creativecommons.org/presskit/icons/nc.svg" alt="NC" style="height: 1em; width: 1em; vertical-align: text-bottom;"><img src="https://mirrors.creativecommons.org/presskit/icons/sa.svg" alt="SA" style="height: 1em; width: 1em; vertical-align: text-bottom;">